"""
iCloud Gaming Manager - Aplicación principal
Gestor completo de instancias de iCloud Gaming con bots inteligentes y control unificado
Incluye sistema de IA con TensorFlow para bots independientes
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import sys
import os

# Agregar el directorio actual al path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.main_window import MainWindow
from core.instance_manager import InstanceManager
from core.config_manager import ConfigManager
from bots.ai_bot import AIBotManager
from utils.logger import Logger

class iCloudGamingApp:
    def __init__(self):
        self.logger = Logger()
        self.config_manager = ConfigManager()
        self.instance_manager = InstanceManager()
        self.ai_bot_manager = AIBotManager(self.instance_manager)
        
        # Configurar la aplicación
        self.setup_app()
        
    def setup_app(self):
        """Configuración inicial de la aplicación"""
        try:
            # Cargar configuración
            self.config = self.config_manager.load_config()
            
            # Inicializar la ventana principal
            self.root = tk.Tk()
            self.main_window = MainWindow(
                self.root, 
                self.instance_manager, 
                self.config_manager
            )
            
            # Configurar eventos de cierre
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            self.logger.info("Aplicación inicializada correctamente")
            
        except Exception as e:
            self.logger.error(f"Error al inicializar la aplicación: {e}")
            messagebox.showerror("Error", f"Error al inicializar: {e}")
            sys.exit(1)
    
    def run(self):
        """Ejecutar la aplicación"""
        try:
            self.logger.info("Iniciando aplicación iCloud Gaming Manager")
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"Error durante la ejecución: {e}")
            messagebox.showerror("Error", f"Error durante la ejecución: {e}")
    
    def on_closing(self):
        """Manejo del cierre de la aplicación"""
        try:
            # Detener todos los bots de IA
            self.ai_bot_manager.stop_all_ai_bots()
            
            # Guardar todos los modelos
            self.ai_bot_manager.save_all_models()
            
            # Detener todas las instancias
            self.instance_manager.stop_all_instances()
            
            # Guardar configuración
            self.config_manager.save_config()
            
            self.logger.info("Aplicación cerrada correctamente")
            self.root.destroy()
            
        except Exception as e:
            self.logger.error(f"Error al cerrar la aplicación: {e}")
            self.root.destroy()

def main():
    """Función principal"""
    try:
        app = iCloudGamingApp()
        app.run()
    except KeyboardInterrupt:
        print("\nAplicación interrumpida por el usuario")
    except Exception as e:
        print(f"Error fatal: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
