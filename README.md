# iCloud Gaming Manager
Una aplicación Python completa para gestionar múltiples instancias de iCloud Gaming con bots inteligentes y control unificado.

## 🚀 Características Principales

### Gestión de Instancias
- Gestión de múltiples instancias de iCloud Gaming
- Control simultáneo de todas las instancias
- Monitoreo en tiempo real del estado
- Configuración personalizada por instancia

### Sistema de Bots Inteligentes 🧠
- **Bots Básicos**: Automatización con scripts y macros predefinidos
- **Bots IA**: Sistema de inteligencia artificial con TensorFlow
  - **Deep Q-Network (DQN)**: Aprendizaje por refuerzo básico
  - **Actor-Critic (A3C)**: Entrenamiento distribuido avanzado
  - **Proximal Policy Optimization (PPO)**: Optimización de políticas
  - **Imitation Learning**: Aprendizaje por imitación
- **Modo <PERSON>**: Los bots pueden operar independientemente
- **Entrenamiento Adaptativo**: Los bots aprenden y mejoran con el tiempo
- **Persistencia de Modelos**: Guardar y cargar modelos entrenados

### Interfaz Gráfica Avanzada
- Interfaz unificada con pestañas especializadas
- Panel de control de instancias
- Panel de gestión de bots (básicos y IA)
- Control de input broadcasting
- Monitoreo de estadísticas en tiempo real
- Visualización del progreso de entrenamiento

## 📦 Instalación

### Requisitos Previos
- Python 3.10+
- Windows (para controles de input)
- Acceso a iCloud Gaming

### Instalación de Dependencias
```bash
pip install -r requirements.txt
```

Las dependencias incluyen:
- **TensorFlow**: Para IA y aprendizaje automático
- **OpenCV**: Para procesamiento de imágenes
- **NumPy**: Para cálculos numéricos
- **Pillow**: Para manipulación de imágenes
- **PyAutoGUI**: Para automatización de UI
- **Scikit-learn**: Para algoritmos de ML adicionales

## 🎮 Uso

### Aplicación Principal
```bash
python main.py
```

### Demostración de IA
```bash
python ai_demo.py
```

### Ejemplo Básico
```bash
python example.py
```

## 🧠 Sistema de IA

### Creación de Bots IA
1. **Desde la GUI**: Usa la pestaña "🧠 Bots IA"
2. **Desde código**:
```python
from bots.ai_bot import AIBotManager, AIModelType

# Crear gestor de bots IA
ai_manager = AIBotManager(instance_manager)

# Crear bot con DQN
bot_id = ai_manager.create_ai_bot(
    instance_id="my_instance",
    model_type=AIModelType.DQN,
    name="MiBot_IA"
)
```

### Entrenamiento
```python
# Entrenar bot específico
ai_manager.start_training_session("my_instance", episodes=100)

# Entrenar todos los bots
ai_manager.start_all_training(episodes=50)
```

### Modo Autónomo
```python
# Activar modo autónomo
ai_manager.start_autonomous_mode("my_instance")

# Activar para todos
ai_manager.start_all_autonomous()
```

### Persistencia de Modelos
```python
# Guardar modelo
ai_bot = ai_manager.get_ai_bot("my_instance")
ai_bot.save_model()

# Cargar modelo
ai_bot.load_model()
```

## 📁 Estructura del Proyecto

```
icloud-gaming-manager/
├── main.py                 # Aplicación principal
├── ai_demo.py             # Demostración de IA
├── example.py             # Ejemplo básico
├── requirements.txt       # Dependencias
├── gui/                   # Interfaz gráfica
│   ├── main_window.py     # Ventana principal
│   ├── bot_panel.py       # Panel de bots (básicos + IA)
│   ├── instance_panel.py  # Panel de instancias
│   └── input_controller.py # Control de input
├── core/                  # Lógica principal
│   ├── instance_manager.py # Gestor de instancias
│   └── config_manager.py   # Gestor de configuración
├── bots/                  # Sistema de bots
│   ├── base_bot.py        # Bot base (abstracto)
│   └── ai_bot.py          # Bots inteligentes con IA
├── config/                # Configuración
│   └── app_config.json    # Configuración de la app
├── utils/                 # Utilidades
│   └── logger.py          # Sistema de logging
└── models/                # Modelos de IA guardados (creado automáticamente)
```

## 🎯 Casos de Uso

### 1. Gestión Básica
- Crear y gestionar múltiples instancias de iCloud Gaming
- Controlar todas las instancias desde una interfaz unificada
- Monitorear el estado en tiempo real

### 2. Automatización con Bots Básicos
- Crear macros y scripts para automatizar tareas repetitivas
- Ejecutar secuencias de acciones predefinidas
- Control de timing y loops

### 3. IA y Aprendizaje Automático
- Entrenar bots para que aprendan a jugar automáticamente
- Desarrollar estrategias adaptativas
- Crear bots que mejoren con el tiempo
- Implementar comportamientos complejos

### 4. Operación Autónoma
- Dejar que los bots operen independientemente
- Monitorear el rendimiento y estadísticas
- Ajustar parámetros según resultados

## 🛠️ Desarrollo y Extensión

### Añadir Nuevos Modelos de IA
```python
# En ai_bot.py
class CustomAIModel:
    def __init__(self, state_size, action_size):
        # Implementar arquitectura personalizada
        pass
```

### Crear Nuevos Tipos de Bots
```python
# Heredar de BaseBot
class CustomBot(BaseBot):
    def execute_action(self, action):
        # Implementar lógica personalizada
        pass
```

## 🔧 Configuración Avanzada

### Parámetros de Entrenamiento
- **Learning Rate**: Velocidad de aprendizaje (default: 0.001)
- **Epsilon**: Exploración vs explotación (default: 1.0 → 0.01)
- **Batch Size**: Tamaño de lote para entrenamiento (default: 32)
- **Memory Size**: Tamaño de la memoria de experiencias (default: 10000)

### Optimización
- **GPU**: TensorFlow detecta automáticamente GPU disponible
- **Memoria**: Ajustar batch_size según disponibilidad
- **Paralelización**: Usar múltiples instancias para entrenamiento distribuido

## 📊 Monitoreo y Estadísticas

La aplicación proporciona métricas detalladas:
- Episodios de entrenamiento completados
- Recompensa promedio por episodio
- Acciones ejecutadas
- Tasa de exploración (epsilon)
- Tamaño de memoria de experiencias
- Tiempo de entrenamiento

## 🤖 Características de IA Avanzadas

### Aprendizaje por Refuerzo
- **Estado**: Captura de pantalla preprocesada
- **Acciones**: Clicks, teclas, movimientos
- **Recompensas**: Basadas en métricas del juego
- **Política**: Estrategia aprendida para maximizar recompensas

### Procesamiento de Imágenes
- Captura automática de pantalla
- Preprocesamiento para redes neuronales
- Detección de elementos de UI
- Análisis de estado del juego

### Persistencia y Transferencia
- Guardar/cargar modelos entrenados
- Transferir conocimiento entre instancias
- Continuar entrenamiento desde checkpoints
- Exportar/importar configuraciones

## 🎨 Interfaz de Usuario

### Pestañas Principales
1. **🏠 Instancias**: Gestión de instancias de juego
2. **🤖 Bots Básicos**: Automatización con scripts
3. **🧠 Bots IA**: Inteligencia artificial y aprendizaje
4. **🎮 Control de Input**: Broadcasting de input
5. **📊 Monitoreo**: Estadísticas y logs en tiempo real

### Funcionalidades de la Pestaña IA
- Crear bots IA con diferentes algoritmos
- Configurar parámetros de entrenamiento
- Iniciar/detener entrenamiento
- Activar modo autónomo
- Guardar/cargar modelos
- Ver estadísticas detalladas
- Monitorear progreso en tiempo real

## 📞 Soporte y Contribución

### Reportar Problemas
- Usar el sistema de logs integrado
- Revisar archivos en `logs/`
- Incluir información de configuración

### Contribuir
- Fork el repositorio
- Crear rama para nueva funcionalidad
- Implementar tests
- Enviar pull request

## 📜 Licencia

Este proyecto está bajo licencia MIT. Ver `LICENSE` para más detalles.
